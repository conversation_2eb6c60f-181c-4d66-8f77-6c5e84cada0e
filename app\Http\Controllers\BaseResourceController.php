<?php

namespace App\Http\Controllers;

use App\Helpers\ImageHelper;
use Illuminate\Http\Request;

abstract class BaseResourceController extends Controller
{
    /**
     * Get the model class name
     */
    abstract protected function getModelClass();

    /**
     * Get the view prefix for admin views
     */
    abstract protected function getViewPrefix();

    /**
     * Get the route prefix for redirects
     */
    abstract protected function getRoutePrefix();

    /**
     * Get validation rules for store/update
     */
    abstract protected function getValidationRules(Request $request, $model = null);

    /**
     * Get the image field name
     */
    protected function getImageField()
    {
        return 'image';
    }

    /**
     * Get the image upload directory
     */
    abstract protected function getImageDirectory();

    /**
     * Handle image upload with validation
     */
    protected function handleImageUpload(Request $request, $fieldName = null, $directory = null, $oldImagePath = null)
    {
        $fieldName = $fieldName ?? $this->getImageField();
        $directory = $directory ?? $this->getImageDirectory();

        if (!$request->hasFile($fieldName)) {
            return null;
        }

        $imageErrors = ImageHelper::validateImage($request->file($fieldName));
        if (!empty($imageErrors)) {
            throw new \Exception(implode(', ', $imageErrors));
        }

        // Delete old image if provided
        if ($oldImagePath) {
            ImageHelper::deleteImage($oldImagePath);
        }

        return ImageHelper::uploadAndResize($request->file($fieldName), $directory);
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $modelClass = $this->getModelClass();
        $variableName = $this->getVariableName();

        if ($request->is('admin/*')) {
            $items = $modelClass::latest()->get();
            return view("admin.{$this->getViewPrefix()}.index", [$variableName => $items]);
        }

        // Public view
        $items = $modelClass::all();
        return view("{$this->getViewPrefix()}.index", [$variableName => $items]);
    }

    /**
     * Get the variable name for views
     */
    protected function getVariableName()
    {
        $modelName = class_basename($this->getModelClass());
        return strtolower($modelName) . 's';
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view("admin.{$this->getViewPrefix()}.create");
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        try {
            $data = $request->validate($this->getValidationRules($request));

            // Handle image upload
            if ($request->hasFile($this->getImageField())) {
                $data[$this->getImageField()] = $this->handleImageUpload($request);
            }

            $modelClass = $this->getModelClass();
            $modelClass::create($data);

            return redirect()->route("admin.{$this->getRoutePrefix()}.index")
                ->with('success', 'เพิ่มข้อมูลสำเร็จ');

        } catch (\Exception $e) {
            \Log::error('Error creating resource: ' . $e->getMessage());

            // If it's an AJAX request, return JSON response
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาดในการบันทึกข้อมูล',
                    'error' => $e->getMessage()
                ], 500);
            }

            return back()->withErrors(['error' => 'เกิดข้อผิดพลาดในการบันทึกข้อมูล'])->withInput();
        }
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit($model)
    {
        return view("admin.{$this->getViewPrefix()}.edit", [
            strtolower(class_basename($model)) => $model
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $model)
    {
        try {
            $data = $request->validate($this->getValidationRules($request, $model));

            // Handle image upload
            if ($request->hasFile($this->getImageField())) {
                $data[$this->getImageField()] = $this->handleImageUpload(
                    $request, 
                    null, 
                    null, 
                    $model->{$this->getImageField()}
                );
            }

            $model->update($data);

            return redirect()->route("admin.{$this->getRoutePrefix()}.index")
                ->with('success', 'อัปเดตข้อมูลสำเร็จ');

        } catch (\Exception $e) {
            \Log::error('Error updating resource: ' . $e->getMessage());

            // If it's an AJAX request, return JSON response
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'เกิดข้อผิดพลาดในการอัปเดตข้อมูล',
                    'error' => $e->getMessage()
                ], 500);
            }

            return back()->withErrors(['error' => 'เกิดข้อผิดพลาดในการอัปเดตข้อมูล'])->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($model)
    {
        try {
            // Delete associated image
            if ($model->{$this->getImageField()}) {
                ImageHelper::deleteImage($model->{$this->getImageField()});
            }

            $model->delete();

            return redirect()->route("admin.{$this->getRoutePrefix()}.index")
                ->with('success', 'ลบข้อมูลสำเร็จ');

        } catch (\Exception $e) {
            \Log::error('Error deleting resource: ' . $e->getMessage());
            return back()->withErrors(['error' => 'เกิดข้อผิดพลาดในการลบข้อมูล']);
        }
    }

    /**
     * Handle AJAX show request for admin
     */
    protected function handleAjaxShow($model, array $fields = [])
    {
        if (request()->ajax() && request()->is('admin/*')) {
            $data = ['id' => $model->id];
            
            foreach ($fields as $field) {
                if ($field === 'image_url' && $model->{$this->getImageField()}) {
                    $data['image_url'] = ImageHelper::getImageUrl($model->{$this->getImageField()});
                } else {
                    $data[$field] = $model->$field;
                }
            }
            
            return response()->json($data);
        }
        
        return null;
    }
}
